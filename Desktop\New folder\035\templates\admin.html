{% extends "base.html" %}

{% block title %}لوحة الإدارة - من سيربح المليون{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <h1 class="text-warning mb-4">
                <i class="fas fa-cog"></i> لوحة الإدارة
            </h1>
        </div>
    </div>

    <!-- Add New Question -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-dark border-success">
                <div class="card-header bg-success text-dark">
                    <h4><i class="fas fa-plus"></i> إضافة سؤال جديد</h4>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('admin') }}">
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="question" class="form-label text-light">Question:</label>
                                <textarea class="form-control" id="question" name="question" rows="3" required></textarea>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="option_a" class="form-label text-light">Option A:</label>
                                <input type="text" class="form-control" id="option_a" name="option_a" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="option_b" class="form-label text-light">Option B:</label>
                                <input type="text" class="form-control" id="option_b" name="option_b" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="option_c" class="form-label text-light">Option C:</label>
                                <input type="text" class="form-control" id="option_c" name="option_c" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="option_d" class="form-label text-light">Option D:</label>
                                <input type="text" class="form-control" id="option_d" name="option_d" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="correct_answer" class="form-label text-light">Correct Answer:</label>
                                <select class="form-control" id="correct_answer" name="correct_answer" required>
                                    <option value="">Select correct answer</option>
                                    <option value="A">A</option>
                                    <option value="B">B</option>
                                    <option value="C">C</option>
                                    <option value="D">D</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="difficulty" class="form-label text-light">Difficulty:</label>
                                <select class="form-control" id="difficulty" name="difficulty" required>
                                    <option value="1">Easy (Questions 1-5)</option>
                                    <option value="2">Medium (Questions 6-10)</option>
                                    <option value="3">Hard (Questions 11-15)</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="category" class="form-label text-light">Category:</label>
                                <input type="text" class="form-control" id="category" name="category" value="General">
                            </div>
                        </div>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-plus"></i> Add Question
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Import from Excel -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-dark border-info">
                <div class="card-header bg-info text-dark">
                    <h4><i class="fas fa-file-excel"></i> Import Questions from Excel</h4>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('admin') }}" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="excel_file" class="form-label text-light">Excel File:</label>
                            <input type="file" class="form-control" id="excel_file" name="excel_file" accept=".xlsx,.xls">
                            <div class="form-text text-muted">
                                Excel file should have columns: question, option_a, option_b, option_c, option_d, correct_answer, difficulty, category
                            </div>
                        </div>
                        <button type="submit" name="import_excel" class="btn btn-info">
                            <i class="fas fa-upload"></i> Import Questions
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Questions List -->
    <div class="row">
        <div class="col-12">
            <div class="card bg-dark border-warning">
                <div class="card-header bg-warning text-dark">
                    <h4><i class="fas fa-list"></i> All Questions ({{ questions|length }} total)</h4>
                </div>
                <div class="card-body">
                    {% if questions %}
                    <div class="table-responsive">
                        <table class="table table-dark table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Question</th>
                                    <th>Options</th>
                                    <th>Correct</th>
                                    <th>Difficulty</th>
                                    <th>Category</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for question in questions %}
                                <tr>
                                    <td>{{ question[0] }}</td>
                                    <td class="question-cell">{{ question[1][:100] }}{% if question[1]|length > 100 %}...{% endif %}</td>
                                    <td>
                                        <small>
                                            <strong>A:</strong> {{ question[2][:30] }}{% if question[2]|length > 30 %}...{% endif %}<br>
                                            <strong>B:</strong> {{ question[3][:30] }}{% if question[3]|length > 30 %}...{% endif %}<br>
                                            <strong>C:</strong> {{ question[4][:30] }}{% if question[4]|length > 30 %}...{% endif %}<br>
                                            <strong>D:</strong> {{ question[5][:30] }}{% if question[5]|length > 30 %}...{% endif %}
                                        </small>
                                    </td>
                                    <td class="text-success"><strong>{{ question[6] }}</strong></td>
                                    <td>
                                        {% if question[7] == 1 %}
                                            <span class="badge bg-success">Easy</span>
                                        {% elif question[7] == 2 %}
                                            <span class="badge bg-warning">Medium</span>
                                        {% else %}
                                            <span class="badge bg-danger">Hard</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ question[8] if question[8] else 'General' }}</td>
                                    <td>
                                        <button class="btn btn-sm btn-info" onclick="viewQuestion({{ question[0] }})">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="deleteQuestion({{ question[0] }})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center text-muted">
                        <i class="fas fa-question-circle fa-3x mb-3"></i>
                        <p>No questions found. Add some questions to get started!</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Question View Modal -->
<div class="modal fade" id="questionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content bg-dark">
            <div class="modal-header border-info">
                <h5 class="modal-title text-info">Question Details</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-light" id="questionDetails">
                <!-- Question details will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content bg-dark">
            <div class="modal-header border-danger">
                <h5 class="modal-title text-danger">Delete Question</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-light">
                <p>Are you sure you want to delete this question? This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="POST" action="{{ url_for('admin') }}" style="display: inline;" id="deleteForm">
                    <input type="hidden" name="delete_question_id" id="deleteQuestionId">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function viewQuestion(questionId) {
    // Find question data from the table
    const questions = {{ questions|tojson }};
    const question = questions.find(q => q[0] === questionId);
    
    if (question) {
        const details = `
            <h6>Question:</h6>
            <p>${question[1]}</p>
            <h6>Options:</h6>
            <ul>
                <li><strong>A:</strong> ${question[2]}</li>
                <li><strong>B:</strong> ${question[3]}</li>
                <li><strong>C:</strong> ${question[4]}</li>
                <li><strong>D:</strong> ${question[5]}</li>
            </ul>
            <h6>Correct Answer: <span class="text-success">${question[6]}</span></h6>
            <h6>Difficulty: ${question[7] === 1 ? 'Easy' : question[7] === 2 ? 'Medium' : 'Hard'}</h6>
            <h6>Category: ${question[8] || 'General'}</h6>
        `;
        document.getElementById('questionDetails').innerHTML = details;
        new bootstrap.Modal(document.getElementById('questionModal')).show();
    }
}

function deleteQuestion(questionId) {
    document.getElementById('deleteQuestionId').value = questionId;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
