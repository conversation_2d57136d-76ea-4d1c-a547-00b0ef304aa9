from flask import Flask, render_template, request, session, redirect, url_for, jsonify, flash
import random
from database import (
    init_database, get_questions_by_difficulty, save_player_score, 
    get_top_players, add_question, get_all_questions, delete_question,
    import_questions_from_excel
)
import os

app = Flask(__name__)
app.secret_key = 'your-secret-key-change-this'

# Prize structure (in dollars)
PRIZE_LEVELS = [
    100, 200, 300, 500, 1000,
    2000, 4000, 8000, 16000, 32000,
    64000, 125000, 250000, 500000, 1000000
]

# Safe havens (guaranteed amounts)
SAFE_HAVENS = [1000, 32000]

@app.route('/')
def index():
    """Main page"""
    top_players = get_top_players(5)
    return render_template('index.html', top_players=top_players)

@app.route('/start_game', methods=['POST'])
def start_game():
    """Start a new game"""
    player_name = request.form.get('player_name', '').strip()
    if not player_name:
        flash('يرجى إدخال اسمك لبدء اللعبة')
        return redirect(url_for('index'))
    
    # Initialize game session
    session['player_name'] = player_name
    session['current_level'] = 0
    session['current_score'] = 0
    session['lifelines'] = {
        'fifty_fifty': True,
        'phone_friend': True,
        'ask_audience': True
    }
    session['game_over'] = False
    
    return redirect(url_for('game'))

@app.route('/game')
def game():
    """Game page"""
    if 'player_name' not in session:
        return redirect(url_for('index'))
    
    if session.get('game_over', False):
        return redirect(url_for('result'))
    
    current_level = session.get('current_level', 0)
    
    # Check if game is completed
    if current_level >= len(PRIZE_LEVELS):
        session['game_over'] = True
        session['current_score'] = PRIZE_LEVELS[-1]
        return redirect(url_for('result'))
    
    # Get difficulty based on level
    if current_level < 5:
        difficulty = 1  # Easy
    elif current_level < 10:
        difficulty = 2  # Medium
    else:
        difficulty = 3  # Hard
    
    # Get questions for current difficulty
    questions = get_questions_by_difficulty(difficulty)
    
    if not questions:
        flash('لا توجد أسئلة متاحة لهذا المستوى')
        return redirect(url_for('index'))
    
    # Select random question
    current_question = random.choice(questions)
    session['current_question'] = {
        'id': current_question[0],
        'question': current_question[1],
        'options': {
            'A': current_question[2],
            'B': current_question[3],
            'C': current_question[4],
            'D': current_question[5]
        },
        'correct_answer': current_question[6]
    }
    
    return render_template('game.html', 
                         question=session['current_question'],
                         current_level=current_level,
                         prize_levels=PRIZE_LEVELS,
                         lifelines=session['lifelines'],
                         player_name=session['player_name'])

@app.route('/answer', methods=['POST'])
def answer():
    """Process player's answer"""
    if 'current_question' not in session:
        return redirect(url_for('index'))
    
    selected_answer = request.form.get('answer')
    correct_answer = session['current_question']['correct_answer']
    current_level = session.get('current_level', 0)
    
    if selected_answer == correct_answer:
        # Correct answer
        session['current_level'] = current_level + 1
        session['current_score'] = PRIZE_LEVELS[current_level]
        
        if current_level + 1 >= len(PRIZE_LEVELS):
            # Game completed - won the million!
            session['game_over'] = True
            return redirect(url_for('result'))
        
        return redirect(url_for('game'))
    else:
        # Wrong answer - game over
        session['game_over'] = True
        
        # Calculate final score based on safe havens
        final_score = 0
        for safe_haven in reversed(SAFE_HAVENS):
            if session['current_score'] >= safe_haven:
                final_score = safe_haven
                break
        
        session['current_score'] = final_score
        return redirect(url_for('result'))

@app.route('/use_lifeline', methods=['POST'])
def use_lifeline():
    """Use a lifeline"""
    lifeline_type = request.form.get('lifeline')
    
    if lifeline_type not in session['lifelines'] or not session['lifelines'][lifeline_type]:
        return jsonify({'error': 'Lifeline not available'})
    
    # Mark lifeline as used
    session['lifelines'][lifeline_type] = False
    
    current_question = session['current_question']
    correct_answer = current_question['correct_answer']
    
    if lifeline_type == 'fifty_fifty':
        # Remove two wrong answers
        options = list(current_question['options'].keys())
        options.remove(correct_answer)
        
        # Randomly select one wrong answer to keep
        wrong_to_keep = random.choice(options)
        options.remove(wrong_to_keep)
        
        # Remove the other two wrong answers
        removed_options = options
        
        return jsonify({
            'type': 'fifty_fifty',
            'removed_options': removed_options
        })
    
    elif lifeline_type == 'ask_audience':
        # Simulate audience poll
        options = list(current_question['options'].keys())
        
        # Give correct answer higher probability
        percentages = {}
        correct_percentage = random.randint(45, 85)
        percentages[correct_answer] = correct_percentage
        
        remaining = 100 - correct_percentage
        for option in options:
            if option != correct_answer:
                percentage = random.randint(1, remaining // 2)
                percentages[option] = percentage
                remaining -= percentage
        
        # Adjust last option to make total 100%
        last_option = [opt for opt in options if opt != correct_answer and opt not in percentages]
        if last_option:
            percentages[last_option[0]] = remaining
        
        return jsonify({
            'type': 'ask_audience',
            'percentages': percentages
        })
    
    elif lifeline_type == 'phone_friend':
        # Simulate friend's advice
        options = list(current_question['options'].keys())
        
        # Friend has 70% chance to give correct answer
        if random.random() < 0.7:
            friend_choice = correct_answer
            confidence = random.randint(70, 95)
        else:
            wrong_options = [opt for opt in options if opt != correct_answer]
            friend_choice = random.choice(wrong_options)
            confidence = random.randint(40, 70)
        
        return jsonify({
            'type': 'phone_friend',
            'friend_choice': friend_choice,
            'confidence': confidence
        })
    
    return jsonify({'error': 'Invalid lifeline'})

@app.route('/walk_away', methods=['POST'])
def walk_away():
    """Player chooses to walk away with current winnings"""
    session['game_over'] = True
    return redirect(url_for('result'))

@app.route('/result')
def result():
    """Game result page"""
    if 'player_name' not in session:
        return redirect(url_for('index'))
    
    player_name = session['player_name']
    final_score = session.get('current_score', 0)
    level_reached = session.get('current_level', 0)
    
    # Save score to database
    save_player_score(player_name, final_score, level_reached)
    
    # Clear session
    session.clear()
    
    return render_template('result.html', 
                         player_name=player_name,
                         final_score=final_score,
                         level_reached=level_reached,
                         prize_levels=PRIZE_LEVELS)

@app.route('/admin', methods=['GET', 'POST'])
def admin():
    """Admin panel"""
    if request.method == 'POST':
        # Handle adding new question
        if 'question' in request.form:
            question = request.form.get('question')
            option_a = request.form.get('option_a')
            option_b = request.form.get('option_b')
            option_c = request.form.get('option_c')
            option_d = request.form.get('option_d')
            correct_answer = request.form.get('correct_answer')
            difficulty = int(request.form.get('difficulty', 1))
            category = request.form.get('category', 'General')

            add_question(question, option_a, option_b, option_c, option_d, correct_answer, difficulty, category)
            flash('Question added successfully!')

        # Handle Excel import
        elif 'import_excel' in request.form:
            excel_file = request.files.get('excel_file')
            if excel_file and excel_file.filename.endswith(('.xlsx', '.xls')):
                try:
                    # Save uploaded file temporarily
                    import tempfile
                    with tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx') as tmp_file:
                        excel_file.save(tmp_file.name)
                        if import_questions_from_excel(tmp_file.name):
                            flash('Questions imported successfully!')
                        else:
                            flash('Error importing questions. Please check the file format.')
                    os.unlink(tmp_file.name)
                except Exception as e:
                    flash(f'Error importing questions: {str(e)}')
            else:
                flash('Please select a valid Excel file (.xlsx or .xls)')

        # Handle question deletion
        elif 'delete_question_id' in request.form:
            question_id = int(request.form.get('delete_question_id'))
            delete_question(question_id)
            flash('Question deleted successfully!')

        return redirect(url_for('admin'))

    questions = get_all_questions()
    return render_template('admin.html', questions=questions)

@app.route('/test_effects')
def test_effects():
    """Test page for sounds and visual effects"""
    return render_template('test_effects.html')

if __name__ == '__main__':
    init_database()
    app.run(debug=True, port=5060)
