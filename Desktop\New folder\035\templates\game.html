{% extends "base.html" %}

{% block title %}Game - Who Wants to Be a Millionaire{% endblock %}

{% block content %}
<div class="game-container">
    <div class="row">
        <!-- Prize Ladder -->
        <div class="col-lg-3">
            <div class="card bg-dark border-warning">
                <div class="card-header bg-warning text-dark">
                    <h5><i class="fas fa-list-ol"></i> Prize Ladder</h5>
                </div>
                <div class="card-body p-2">
                    <div class="prize-ladder">
                        {% for i in range(prize_levels|length - 1, -1, -1) %}
                        <div class="prize-level {% if i == current_level %}current{% elif i < current_level %}completed{% endif %}">
                            <span class="level-number">{{ i + 1 }}</span>
                            <span class="prize-amount">${{ "{:,}".format(prize_levels[i]) }}</span>
                            {% if prize_levels[i] in [1000, 32000] %}
                            <i class="fas fa-shield-alt text-success safe-haven-icon" title="Safe Haven"></i>
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Game Area -->
        <div class="col-lg-6">
            <div class="game-area">
                <!-- Player Info -->
                <div class="card bg-dark border-info mb-3">
                    <div class="card-body text-center">
                        <h4 class="text-info">{{ player_name }}</h4>
                        <p class="mb-0">Question {{ current_level + 1 }} of 15</p>
                        <p class="text-warning mb-0">Playing for: ${{ "{:,}".format(prize_levels[current_level]) }}</p>
                    </div>
                </div>

                <!-- Question -->
                <div class="card bg-dark border-light">
                    <div class="card-body">
                        <h3 class="question-text text-center text-light mb-4">
                            {{ question.question }}
                        </h3>
                        
                        <form id="answerForm" method="POST" action="{{ url_for('answer') }}">
                            <div class="row">
                                {% for option_key, option_text in question.options.items() %}
                                <div class="col-md-6 mb-3">
                                    <button type="button" class="btn btn-outline-light btn-lg w-100 answer-btn" 
                                            data-answer="{{ option_key }}" id="option{{ option_key }}">
                                        <strong>{{ option_key }}:</strong> {{ option_text }}
                                    </button>
                                </div>
                                {% endfor %}
                            </div>
                            <input type="hidden" name="answer" id="selectedAnswer">
                        </form>

                        <div class="text-center mt-4">
                            <button type="button" class="btn btn-secondary" onclick="walkAway()">
                                <i class="fas fa-walking"></i> Walk Away
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Lifelines -->
        <div class="col-lg-3">
            <div class="card bg-dark border-success">
                <div class="card-header bg-success text-dark">
                    <h5><i class="fas fa-life-ring"></i> Lifelines</h5>
                </div>
                <div class="card-body">
                    <div class="lifelines">
                        <button type="button" class="btn btn-outline-warning w-100 mb-2 lifeline-btn" 
                                data-lifeline="fifty_fifty" 
                                {% if not lifelines.fifty_fifty %}disabled{% endif %}>
                            <i class="fas fa-cut"></i> 50:50
                        </button>
                        
                        <button type="button" class="btn btn-outline-info w-100 mb-2 lifeline-btn" 
                                data-lifeline="phone_friend" 
                                {% if not lifelines.phone_friend %}disabled{% endif %}>
                            <i class="fas fa-phone"></i> Phone a Friend
                        </button>
                        
                        <button type="button" class="btn btn-outline-success w-100 mb-2 lifeline-btn" 
                                data-lifeline="ask_audience" 
                                {% if not lifelines.ask_audience %}disabled{% endif %}>
                            <i class="fas fa-users"></i> Ask the Audience
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Lifeline Modals -->
<!-- 50:50 Modal -->
<div class="modal fade" id="fiftyFiftyModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content bg-dark">
            <div class="modal-header border-warning">
                <h5 class="modal-title text-warning">50:50 Lifeline</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-light">
                <p>Two incorrect answers have been removed!</p>
            </div>
        </div>
    </div>
</div>

<!-- Phone a Friend Modal -->
<div class="modal fade" id="phoneFriendModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content bg-dark">
            <div class="modal-header border-info">
                <h5 class="modal-title text-info">Phone a Friend</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-light">
                <p id="friendAdvice"></p>
            </div>
        </div>
    </div>
</div>

<!-- Ask the Audience Modal -->
<div class="modal fade" id="askAudienceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content bg-dark">
            <div class="modal-header border-success">
                <h5 class="modal-title text-success">Ask the Audience</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-light">
                <p>Here's what the audience thinks:</p>
                <div id="audienceResults"></div>
            </div>
        </div>
    </div>
</div>

<!-- Walk Away Confirmation Modal -->
<div class="modal fade" id="walkAwayModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content bg-dark">
            <div class="modal-header border-secondary">
                <h5 class="modal-title text-secondary">Walk Away?</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-light">
                <p>Are you sure you want to walk away with your current winnings?</p>
                <p class="text-warning">You will leave with: ${{ "{:,}".format(prize_levels[current_level-1] if current_level > 0 else 0) }}</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Continue Playing</button>
                <form method="POST" action="{{ url_for('walk_away') }}" style="display: inline;">
                    <button type="submit" class="btn btn-warning">Walk Away</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Game JavaScript will be added to the external file
</script>
{% endblock %}
