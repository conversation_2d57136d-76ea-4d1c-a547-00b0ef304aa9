# Who Wants to Be a Millionaire - Web Game

A web-based implementation of the popular "Who Wants to Be a Millionaire" game using Python Flask.

## Features

- **Interactive Web Interface**: Modern, responsive design with Bootstrap
- **15 Progressive Questions**: From $100 to $1,000,000
- **Three Lifelines**: 50:50, Phone a Friend, Ask the Audience
- **Safe Havens**: Guaranteed amounts at $1,000 and $32,000
- **Admin Panel**: Add, edit, and manage questions
- **Excel Import**: Bulk import questions from Excel files
- **Player Statistics**: Track top players and scores
- **Arabic Language Support**: Ready for localization
- **Sound Effects**: Optional audio feedback (add sound files)

## Installation

1. **Clone or download the project**
   ```bash
   cd "Desktop\New folder\035"
   ```

2. **Install Python dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Initialize the database with sample questions**
   ```bash
   python init_questions.py
   ```

4. **Run the application**
   ```bash
   python app.py
   ```

5. **Open your browser and go to**
   ```
   http://localhost:5060
   ```

## Project Structure

```
millionaire_game/
├── app.py                 # Main Flask application
├── database.py           # Database operations
├── init_questions.py     # Initialize sample questions
├── requirements.txt      # Python dependencies
├── database.db          # SQLite database (created automatically)
├── questions.xlsx        # Sample Excel file for import
├── templates/            # HTML templates
│   ├── base.html        # Base template
│   ├── index.html       # Welcome page
│   ├── game.html        # Game interface
│   ├── result.html      # Results page
│   └── admin.html       # Admin panel
├── static/              # Static files
│   ├── css/
│   │   └── style.css    # Custom styles
│   ├── js/
│   │   └── game.js      # Game JavaScript
│   ├── images/          # Game images (add your own)
│   └── sounds/          # Sound effects (add your own)
└── README.md            # This file
```

## How to Play

1. **Enter your name** on the welcome page
2. **Answer 15 questions** correctly to win $1,000,000
3. **Use lifelines** strategically:
   - **50:50**: Removes two wrong answers
   - **Phone a Friend**: Get advice from a simulated friend
   - **Ask the Audience**: See audience poll results
4. **Walk away** anytime to keep your current winnings
5. **Safe havens** at $1,000 and $32,000 guarantee minimum winnings

## Prize Structure

| Level | Prize Amount | Difficulty |
|-------|-------------|------------|
| 1     | $100        | Easy       |
| 2     | $200        | Easy       |
| 3     | $300        | Easy       |
| 4     | $500        | Easy       |
| 5     | $1,000      | Easy (Safe Haven) |
| 6     | $2,000      | Medium     |
| 7     | $4,000      | Medium     |
| 8     | $8,000      | Medium     |
| 9     | $16,000     | Medium     |
| 10    | $32,000     | Medium (Safe Haven) |
| 11    | $64,000     | Hard       |
| 12    | $125,000    | Hard       |
| 13    | $250,000    | Hard       |
| 14    | $500,000    | Hard       |
| 15    | $1,000,000  | Hard       |

## Admin Panel

Access the admin panel at `http://localhost:5060/admin` to:

- **Add new questions** manually
- **Import questions** from Excel files
- **View all questions** in the database
- **Delete questions** as needed

### Excel Import Format

Your Excel file should have these columns:
- `question`: The question text
- `option_a`: First option
- `option_b`: Second option
- `option_c`: Third option
- `option_d`: Fourth option
- `correct_answer`: Correct answer (A, B, C, or D)
- `difficulty`: Difficulty level (1=Easy, 2=Medium, 3=Hard)
- `category`: Question category (optional)

## Customization

### Adding Sound Effects

1. Add sound files to `static/sounds/` directory:
   - `question.mp3`: Question reveal sound
   - `select.mp3`: Answer selection sound
   - `lifeline.mp3`: Lifeline activation sound
   - `correct.mp3`: Correct answer sound
   - `incorrect.mp3`: Wrong answer sound

### Adding Images

1. Add images to `static/images/` directory
2. Update CSS or templates to reference your images

### Changing Styling

1. Edit `static/css/style.css` to customize the appearance
2. The design uses Bootstrap 5 for responsive layout

### Adding Languages

1. Use Flask-Babel for internationalization
2. Create translation files for different languages
3. Update templates with translation markers

## Database Schema

### Questions Table
- `id`: Primary key
- `question`: Question text
- `option_a`, `option_b`, `option_c`, `option_d`: Answer options
- `correct_answer`: Correct answer (A, B, C, or D)
- `difficulty`: Difficulty level (1-3)
- `category`: Question category

### Players Table
- `id`: Primary key
- `name`: Player name
- `score`: Final score
- `level_reached`: Highest level reached
- `timestamp`: Game completion time

## Technical Requirements

- Python 3.9+
- Flask 2.3+
- SQLite3 (included with Python)
- pandas (for Excel import)
- openpyxl (for Excel file handling)

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is open source and available under the MIT License.

## Support

For questions or issues, please create an issue in the project repository.

## Future Enhancements

- [ ] Multiplayer support
- [ ] Real-time leaderboards
- [ ] Question difficulty balancing
- [ ] Mobile app version
- [ ] Social media integration
- [ ] Custom game modes
- [ ] Question statistics and analytics
