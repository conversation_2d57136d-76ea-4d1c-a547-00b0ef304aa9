{% extends "base.html" %}

{% block title %}انتهت اللعبة - من سيربح المليون{% endblock %}

{% block content %}
<div class="result-container">
    <div class="container text-center">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                {% if final_score == 1000000 %}
                <!-- Million Dollar Winner -->
                <div class="winner-celebration">
                    <h1 class="display-1 text-warning mb-4">
                        <i class="fas fa-trophy"></i>
                        مبروك!
                    </h1>
                    <h2 class="text-light mb-4">{{ player_name }}، أنت مليونير!</h2>
                    <div class="final-score-million">
                        <h3 class="text-warning">الأرباح النهائية: $1,000,000</h3>
                    </div>
                    <div class="confetti-animation">
                        <i class="fas fa-star text-warning"></i>
                        <i class="fas fa-star text-info"></i>
                        <i class="fas fa-star text-success"></i>
                    </div>
                </div>
                {% else %}
                <!-- Regular Game Over -->
                <div class="game-over">
                    <h1 class="display-2 text-info mb-4">
                        <i class="fas fa-flag-checkered"></i>
                        انتهت اللعبة
                    </h1>
                    <h2 class="text-light mb-4">أحسنت اللعب، {{ player_name }}!</h2>
                    
                    <div class="card bg-dark border-warning mb-4">
                        <div class="card-body">
                            <h3 class="card-title text-warning">نتائجك</h3>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="result-stat">
                                        <h4 class="text-success">الأرباح النهائية</h4>
                                        <p class="display-4 text-warning">${{ "{:,}".format(final_score) }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="result-stat">
                                        <h4 class="text-info">المستوى المُحقق</h4>
                                        <p class="display-4 text-light">{{ level_reached }} / 15</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {% if final_score > 0 %}
                    <div class="alert alert-success" role="alert">
                        <i class="fas fa-shield-alt"></i>
                        You reached a safe haven and secured your winnings!
                    </div>
                    {% endif %}
                </div>
                {% endif %}

                <!-- Performance Analysis -->
                <div class="card bg-dark border-info mb-4">
                    <div class="card-header bg-info text-dark">
                        <h4><i class="fas fa-chart-line"></i> Performance Analysis</h4>
                    </div>
                    <div class="card-body text-light">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="performance-metric">
                                    <h5>Questions Answered</h5>
                                    <p class="h3 text-success">{{ level_reached }}</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="performance-metric">
                                    <h5>Success Rate</h5>
                                    <p class="h3 text-info">{{ "%.1f"|format((level_reached / 15) * 100) }}%</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="performance-metric">
                                    <h5>Prize Efficiency</h5>
                                    <p class="h3 text-warning">{{ "%.1f"|format((final_score / 1000000) * 100) }}%</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Prize Ladder Review -->
                <div class="card bg-dark border-secondary mb-4">
                    <div class="card-header bg-secondary text-dark">
                        <h4><i class="fas fa-list-ol"></i> Your Journey</h4>
                    </div>
                    <div class="card-body">
                        <div class="prize-ladder-review">
                            {% for i in range(prize_levels|length - 1, -1, -1) %}
                            <div class="prize-level-review 
                                {% if i < level_reached %}completed
                                {% elif i == level_reached %}failed
                                {% else %}not-reached{% endif %}">
                                <span class="level-number">{{ i + 1 }}</span>
                                <span class="prize-amount">${{ "{:,}".format(prize_levels[i]) }}</span>
                                {% if i < level_reached %}
                                    <i class="fas fa-check text-success"></i>
                                {% elif i == level_reached %}
                                    <i class="fas fa-times text-danger"></i>
                                {% else %}
                                    <i class="fas fa-minus text-muted"></i>
                                {% endif %}
                                {% if prize_levels[i] in [1000, 32000] %}
                                <i class="fas fa-shield-alt text-success safe-haven-icon" title="Safe Haven"></i>
                                {% endif %}
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="action-buttons">
                    <a href="{{ url_for('index') }}" class="btn btn-warning btn-lg me-3">
                        <i class="fas fa-redo"></i> Play Again
                    </a>
                    <a href="{{ url_for('index') }}" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-home"></i> Home
                    </a>
                </div>

                <!-- Social Share (Optional) -->
                <div class="social-share mt-4">
                    <p class="text-muted">Share your achievement:</p>
                    <button class="btn btn-outline-primary me-2" onclick="shareResult('facebook')">
                        <i class="fab fa-facebook"></i> Facebook
                    </button>
                    <button class="btn btn-outline-info me-2" onclick="shareResult('twitter')">
                        <i class="fab fa-twitter"></i> Twitter
                    </button>
                    <button class="btn btn-outline-success" onclick="copyResult()">
                        <i class="fas fa-copy"></i> Copy Result
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

{% if final_score == 1000000 %}
<!-- Celebration Animation for Million Dollar Winner -->
<style>
.confetti-animation {
    animation: celebrate 2s infinite;
}

@keyframes celebrate {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.winner-celebration {
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from { text-shadow: 0 0 20px #ffc107; }
    to { text-shadow: 0 0 30px #ffc107, 0 0 40px #ffc107; }
}
</style>

<script>
// Trigger millionaire effects
document.addEventListener('DOMContentLoaded', function() {
    if (window.showMillionaireEffect) {
        showMillionaireEffect();
    }
    if (window.playMillionaireSound) {
        setTimeout(() => playMillionaireSound(), 500);
    }
});
</script>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
function shareResult(platform) {
    const text = `I just {{ 'won $1,000,000' if final_score == 1000000 else 'scored $' + "{:,}".format(final_score) }} on Who Wants to Be a Millionaire!`;
    const url = window.location.origin;
    
    if (platform === 'facebook') {
        window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}&quote=${text}`, '_blank');
    } else if (platform === 'twitter') {
        window.open(`https://twitter.com/intent/tweet?text=${text}&url=${url}`, '_blank');
    }
}

function copyResult() {
    const text = `I just {{ 'won $1,000,000' if final_score == 1000000 else 'scored $' + "{:,}".format(final_score) }} on Who Wants to Be a Millionaire! Level reached: {{ level_reached }}/15`;
    navigator.clipboard.writeText(text).then(() => {
        alert('Result copied to clipboard!');
    });
}
</script>
{% endblock %}
