/* Who Wants to Be a Millionaire - Custom Styles */

/* Global Styles */
body {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    min-height: 100vh;
    font-family: '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Segoe UI', sans-serif;
    direction: rtl;
    text-align: right;
}

/* Arabic Font Support */
* {
    font-family: 'Aria<PERSON>', '<PERSON>homa', 'Segoe UI', sans-serif;
}

/* RTL Adjustments */
.navbar-nav {
    margin-right: auto !important;
    margin-left: 0 !important;
}

.text-left {
    text-align: right !important;
}

.text-right {
    text-align: left !important;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)), 
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffc107" stop-opacity="0.1"/><stop offset="100%" stop-color="%23000" stop-opacity="0.8"/></radialGradient></defs><rect width="100%" height="100%" fill="url(%23a)"/></svg>');
    background-size: cover;
    background-position: center;
    padding: 100px 0;
    margin-bottom: 50px;
}

.game-start-card {
    margin-top: 30px;
}

/* Prize Structure */
.prize-list {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.prize-item {
    background: linear-gradient(90deg, #343a40 0%, #495057 100%);
    color: #fff;
    padding: 8px 15px;
    border-radius: 5px;
    border-left: 4px solid #6c757d;
    font-weight: bold;
    transition: all 0.3s ease;
}

.prize-item.safe-haven {
    border-left-color: #28a745;
    background: linear-gradient(90deg, #155724 0%, #28a745 100%);
    box-shadow: 0 0 10px rgba(40, 167, 69, 0.3);
}

.prize-item:hover {
    transform: translateX(5px);
    box-shadow: 0 2px 10px rgba(255, 193, 7, 0.3);
}

/* Game Area Styles */
.game-container {
    padding: 20px 0;
}

.prize-ladder {
    max-height: 600px;
    overflow-y: auto;
}

.prize-level {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    margin-bottom: 2px;
    border-radius: 5px;
    background: #343a40;
    border-left: 3px solid #6c757d;
    transition: all 0.3s ease;
}

.prize-level.current {
    background: linear-gradient(90deg, #ffc107 0%, #ffca2c 100%);
    color: #000;
    border-left-color: #ffc107;
    box-shadow: 0 0 15px rgba(255, 193, 7, 0.5);
    animation: pulse 2s infinite;
}

.prize-level.completed {
    background: linear-gradient(90deg, #28a745 0%, #34ce57 100%);
    color: #fff;
    border-left-color: #28a745;
}

.level-number {
    font-weight: bold;
    min-width: 25px;
}

.prize-amount {
    font-weight: bold;
    flex-grow: 1;
    text-align: right;
}

.safe-haven-icon {
    margin-left: 5px;
}

@keyframes pulse {
    0% { box-shadow: 0 0 15px rgba(255, 193, 7, 0.5); }
    50% { box-shadow: 0 0 25px rgba(255, 193, 7, 0.8); }
    100% { box-shadow: 0 0 15px rgba(255, 193, 7, 0.5); }
}

/* Question Styles */
.question-text {
    font-size: 1.3rem;
    line-height: 1.6;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    border: 2px solid #ffc107;
}

.answer-btn {
    height: 80px;
    font-size: 1rem;
    text-align: left;
    padding: 15px;
    border: 2px solid #6c757d;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.answer-btn:hover {
    border-color: #ffc107;
    background-color: rgba(255, 193, 7, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
}

.answer-btn.selected {
    background-color: #ffc107;
    color: #000;
    border-color: #ffc107;
    box-shadow: 0 0 20px rgba(255, 193, 7, 0.6);
}

.answer-btn.correct {
    background-color: #28a745;
    border-color: #28a745;
    color: #fff;
    animation: correctAnswer 1s ease-in-out;
}

.answer-btn.incorrect {
    background-color: #dc3545;
    border-color: #dc3545;
    color: #fff;
    animation: incorrectAnswer 1s ease-in-out;
}

.answer-btn.eliminated {
    opacity: 0.3;
    background-color: #6c757d;
    border-color: #6c757d;
    pointer-events: none;
}

@keyframes correctAnswer {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes incorrectAnswer {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Lifelines */
.lifeline-btn {
    height: 60px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.lifeline-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
}

.lifeline-btn:disabled {
    opacity: 0.3;
    background-color: #6c757d;
    border-color: #6c757d;
}

/* Result Page Styles */
.result-container {
    padding: 50px 0;
}

.final-score-million {
    background: linear-gradient(45deg, #ffc107, #ffca2c);
    color: #000;
    padding: 30px;
    border-radius: 15px;
    margin: 30px 0;
    box-shadow: 0 0 30px rgba(255, 193, 7, 0.6);
}

.result-stat {
    text-align: center;
    padding: 20px;
}

.performance-metric {
    text-align: center;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    margin-bottom: 15px;
}

.prize-ladder-review {
    max-height: 400px;
    overflow-y: auto;
}

.prize-level-review {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    margin-bottom: 2px;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.prize-level-review.completed {
    background: linear-gradient(90deg, #28a745 0%, #34ce57 100%);
    color: #fff;
}

.prize-level-review.failed {
    background: linear-gradient(90deg, #dc3545 0%, #e74c3c 100%);
    color: #fff;
}

.prize-level-review.not-reached {
    background: #6c757d;
    color: #fff;
    opacity: 0.6;
}

/* Admin Panel Styles */
.question-cell {
    max-width: 300px;
    word-wrap: break-word;
}

/* Audience Poll Styles */
.audience-poll {
    margin: 20px 0;
}

.poll-option {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.poll-bar {
    height: 30px;
    background: linear-gradient(90deg, #007bff 0%, #0056b3 100%);
    border-radius: 15px;
    margin-left: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    transition: width 1s ease-in-out;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        padding: 50px 0;
    }
    
    .question-text {
        font-size: 1.1rem;
        padding: 15px;
    }
    
    .answer-btn {
        height: auto;
        min-height: 60px;
        font-size: 0.9rem;
        margin-bottom: 10px;
    }
    
    .prize-ladder {
        max-height: 300px;
    }
    
    .display-3 {
        font-size: 2rem;
    }
    
    .display-4 {
        font-size: 1.5rem;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #ffc107;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Custom Scrollbar */
.prize-ladder::-webkit-scrollbar,
.prize-ladder-review::-webkit-scrollbar {
    width: 8px;
}

.prize-ladder::-webkit-scrollbar-track,
.prize-ladder-review::-webkit-scrollbar-track {
    background: #343a40;
    border-radius: 4px;
}

.prize-ladder::-webkit-scrollbar-thumb,
.prize-ladder-review::-webkit-scrollbar-thumb {
    background: #ffc107;
    border-radius: 4px;
}

.prize-ladder::-webkit-scrollbar-thumb:hover,
.prize-ladder-review::-webkit-scrollbar-thumb:hover {
    background: #ffca2c;
}
